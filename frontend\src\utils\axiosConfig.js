/**
 * Configuration Axios pour Poultray DZ
 * Ce fichier configure une instance Axios avec des intercepteurs pour gérer l'authentification
 * et les erreurs de manière cohérente dans toute l'application.
 */

import axios from 'axios';

// URL de base de l'API (sans /api pour éviter la duplication)
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3003';

// Configuration des retries
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 seconde entre chaque retry

// Création d'une instance Axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000, // 30 secondes de timeout
  headers: {
    'Content-Type': 'application/json'
  }
});

// Fonction utilitaire pour attendre un délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Intercepteur pour les requêtes - ajoute le token et /api prefix
axiosInstance.interceptors.request.use(
  config => {
    // Ajouter le compteur de retries à la configuration
    config.retryCount = config.retryCount || 0;

    // Ajouter /api au début de l'URL si ce n'est pas déjà fait
    if (!config.url.startsWith('/api') && !config.url.startsWith('http')) {
      config.url = `/api${config.url}`;
    }

    const token = localStorage.getItem('token');
    if (token) {
      // Utiliser à la fois l'en-tête standard Authorization et x-auth-token
      config.headers['Authorization'] = `Bearer ${token}`;
      config.headers['x-auth-token'] = token;
    }

    console.log(`🌐 Axios Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Intercepteur pour les réponses - gère les erreurs 401 et le rafraîchissement du token
axiosInstance.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    // Vérifier si la requête peut être retentée
    if (error.code === 'ECONNABORTED' && originalRequest.retryCount < MAX_RETRIES) {
      originalRequest.retryCount += 1;
      console.log(`Retry attempt ${originalRequest.retryCount} for ${originalRequest.url}`);

      // Attendre avant de retenter
      await wait(RETRY_DELAY * originalRequest.retryCount);
      return axiosInstance(originalRequest);
    }

    // Si l'erreur est 401 (Non autorisé) et que la requête n'a pas déjà été retentée
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Récupérer les informations Firebase stockées
        const firebaseData = JSON.parse(localStorage.getItem('firebase_data'));

        if (!firebaseData || !firebaseData.email || !firebaseData.token || !firebaseData.uid) {
          console.error('Données Firebase manquantes pour le rafraîchissement du token');
          localStorage.removeItem('token');
          localStorage.removeItem('firebase_data');
          window.location.href = '/login';
          return Promise.reject(error);
        }

        // Tenter de rafraîchir le token avec les informations Firebase
        const response = await axiosInstance.post('/auth/refresh-token', {
          email: firebaseData.email,
          firebase_token: firebaseData.token,
          firebase_uid: firebaseData.uid
        });

        if (response.data.token) {
          localStorage.setItem('token', response.data.token);
          originalRequest.headers['Authorization'] = `Bearer ${response.data.token}`;
          originalRequest.headers['x-auth-token'] = response.data.token;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        console.error('Erreur lors du rafraîchissement du token:', refreshError);
        localStorage.removeItem('token');
        localStorage.removeItem('user_credentials');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Fonction utilitaire pour obtenir les en-têtes d'authentification
export const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  if (!token) return {};

  return {
    'Authorization': `Bearer ${token}`,
    'x-auth-token': token
  };
};

// Exporter l'instance configurée
export default axiosInstance;
