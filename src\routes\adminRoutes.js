const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const { auth } = require('../middleware/auth');
const admin = require('../middleware/admin');
const roleController = require('../controllers/roleController');
const subscriptionPlanController = require('../controllers/subscriptionPlanController');

// Configuration de la connexion à la base de données
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

// Appliquer les middlewares d'authentification et d'admin à toutes les routes
router.use(auth);
router.use(admin);

// Route pour obtenir les données de plateforme avec filtres
router.get('/data', async (req, res) => {
  try {
    const { type = 'sales', timeRange = 'month' } = req.query;

    // Calculer la période en fonction du timeRange
    let dateFilter = '';
    switch (timeRange) {
      case 'day':
        dateFilter = "AND created_at >= NOW() - INTERVAL '1 day'";
        break;
      case 'week':
        dateFilter = "AND created_at >= NOW() - INTERVAL '7 days'";
        break;
      case 'month':
        dateFilter = "AND created_at >= NOW() - INTERVAL '30 days'";
        break;
      case 'year':
        dateFilter = "AND created_at >= NOW() - INTERVAL '365 days'";
        break;
      default:
        dateFilter = "AND created_at >= NOW() - INTERVAL '30 days'";
    }

    let data = [];
    let total = 0;
    let growth = 0;

    switch (type) {
      case 'sales':
        // Données de ventes simulées (à remplacer par vraies données)
        const salesQuery = `
          SELECT
            DATE(created_at) as date,
            COUNT(*) as value
          FROM ventes
          WHERE 1=1 ${dateFilter}
          GROUP BY DATE(created_at)
          ORDER BY date
        `;

        try {
          const salesResult = await pool.query(salesQuery);
          data = salesResult.rows;
          total = data.reduce((sum, item) => sum + parseInt(item.value), 0);
        } catch (err) {
          // Si la table ventes n'existe pas, retourner des données simulées
          data = generateMockData(timeRange, 'sales');
          total = data.reduce((sum, item) => sum + item.value, 0);
        }
        break;

      case 'users':
        const usersQuery = `
          SELECT
            DATE(created_at) as date,
            COUNT(*) as value
          FROM users
          WHERE 1=1 ${dateFilter}
          GROUP BY DATE(created_at)
          ORDER BY date
        `;

        const usersResult = await pool.query(usersQuery);
        data = usersResult.rows;
        total = data.reduce((sum, item) => sum + parseInt(item.value), 0);
        break;

      case 'revenue':
        // Données de revenus simulées
        data = generateMockData(timeRange, 'revenue');
        total = data.reduce((sum, item) => sum + item.value, 0);
        break;

      default:
        data = generateMockData(timeRange, type);
        total = data.reduce((sum, item) => sum + item.value, 0);
    }

    // Calculer la croissance (simulée)
    growth = Math.random() * 20 - 5; // Entre -5% et +15%

    res.json({
      data,
      total,
      growth: parseFloat(growth.toFixed(2)),
      type,
      timeRange
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des données de plateforme:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des données de plateforme',
      error: error.message
    });
  }
});

// Fonction utilitaire pour générer des données simulées
function generateMockData(timeRange, type) {
  const data = [];
  const now = new Date();
  let days = 30;

  switch (timeRange) {
    case 'day': days = 1; break;
    case 'week': days = 7; break;
    case 'month': days = 30; break;
    case 'year': days = 365; break;
  }

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);

    let value;
    switch (type) {
      case 'sales':
        value = Math.floor(Math.random() * 50) + 10;
        break;
      case 'users':
        value = Math.floor(Math.random() * 20) + 5;
        break;
      case 'revenue':
        value = Math.floor(Math.random() * 10000) + 2000;
        break;
      default:
        value = Math.floor(Math.random() * 100) + 10;
    }

    data.push({
      date: date.toISOString().split('T')[0],
      value
    });
  }

  return data;
}

// Route pour obtenir les statistiques générales
router.get('/stats', async (req, res) => {
  try {
    // Vérifier si la table ventes existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'ventes'
      )
    `);

    const ventesTableExists = tableExists.rows[0].exists;
    console.log('Table ventes existe:', ventesTableExists);

    // Récupérer le nombre total d'utilisateurs par rôle
    const userStats = await pool.query(
      `SELECT role, COUNT(*) as count
       FROM users
       GROUP BY role`
    );

    // Récupérer les statistiques des volailles
    let volailleStats;
    try {
      // Vérifier les colonnes de la table volailles
      const columnsQuery = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'volailles'
      `);

      const columns = columnsQuery.rows.map(row => row.column_name);
      console.log('Colonnes de la table volailles:', columns);

      // Vérifier si la colonne 'statut' ou 'active' existe
      const statusColumn = columns.includes('statut') ? 'statut' :
                          (columns.includes('active') ? 'active' : null);

      let whereClause = '';
      if (statusColumn) {
        if (statusColumn === 'statut') {
          whereClause = `WHERE statut = 'disponible'`;
        } else if (statusColumn === 'active') {
          whereClause = `WHERE active = true`;
        }
      }

      volailleStats = await pool.query(`
        SELECT
          COUNT(*) as total_volailles,
          COUNT(DISTINCT eleveur_id) as total_eleveurs,
          COALESCE(AVG(prix_unitaire), 0) as prix_moyen
        FROM volailles
        ${whereClause}
      `);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques de volailles:', err);
      volailleStats = { rows: [{ total_volailles: 0, total_eleveurs: 0, prix_moyen: 0 }] };
    }

    // Récupérer les statistiques des ventes
    let venteStats;
    if (ventesTableExists) {
      try {
        venteStats = await pool.query(
          `SELECT
            COUNT(*) as total_ventes,
            COALESCE(SUM(montant_total), 0) as chiffre_affaires_total
           FROM ventes
           WHERE date_vente >= NOW() - INTERVAL '30 days'`
        );
      } catch (err) {
        console.error('Erreur lors de la récupération des statistiques de ventes:', err);
        venteStats = { rows: [{ total_ventes: 0, chiffre_affaires_total: 0 }] };
      }
    } else {
      venteStats = { rows: [{ total_ventes: 0, chiffre_affaires_total: 0 }] };
    }

    // Calculer le nombre total d'utilisateurs par rôle
    const roleStats = {};
    userStats.rows.forEach(row => {
      roleStats[row.role] = parseInt(row.count);
    });

    res.json({
      users: userStats.rows,
      volailles: volailleStats.rows[0],
      ventes: venteStats.rows[0],
      totalEleveurs: roleStats['eleveur'] || 0,
      totalVeterinaires: roleStats['veterinaire'] || 0,
      totalMarchands: roleStats['marchand'] || 0,
      totalAdmins: roleStats['admin'] || 0,
      totalUsers: userStats.rows.reduce((sum, row) => sum + parseInt(row.count), 0)
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
});

// Route pour obtenir la liste des utilisateurs avec pagination
router.get('/users', async (req, res) => {
  try {
    const { role, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let countWhereClause = '';
    const params = [];
    const countParams = [];

    if (role) {
      whereClause = ' WHERE role = $1';
      countWhereClause = ' WHERE role = $1';
      params.push(role);
      countParams.push(role);
    }

    // Requête pour compter le total
    const countQuery = `SELECT COUNT(*) as total FROM users${countWhereClause}`;
    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    // Requête pour récupérer les utilisateurs
    const usersQuery = `
      SELECT id, username, email, first_name, last_name, role, phone, address, created_at, updated_at
      FROM users${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    params.push(limit, offset);
    const usersResult = await pool.query(usersQuery, params);

    res.json({
      users: usersResult.rows,
      total: total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des utilisateurs',
      error: error.message
    });
  }
});

// Routes pour les rôles
router.get('/roles', roleController.getAllRoles);
router.get('/roles/:id', roleController.getRoleById);
router.post('/roles', roleController.createRole);
router.put('/roles/:id', roleController.updateRole);
router.delete('/roles/:id', roleController.deleteRole);

// Routes pour les plans d'abonnement
router.get('/subscription-plans', subscriptionPlanController.getAllPlans);
router.get('/subscription-plans/:id', subscriptionPlanController.getPlanById);
router.post('/subscription-plans', subscriptionPlanController.createPlan);
router.put('/subscription-plans/:id', subscriptionPlanController.updatePlan);
router.delete('/subscription-plans/:id', subscriptionPlanController.deletePlan);

// Routes pour les notifications admin
router.get('/notifications', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // Récupérer toutes les notifications avec pagination
    const notificationsQuery = `
      SELECT n.*, u.username, u.email
      FROM notifications n
      LEFT JOIN users u ON n.user_id = u.id
      ORDER BY n.created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const countQuery = 'SELECT COUNT(*) FROM notifications';

    const [notificationsResult, countResult] = await Promise.all([
      pool.query(notificationsQuery, [limit, offset]),
      pool.query(countQuery)
    ]);

    const totalNotifications = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalNotifications / limit);

    res.json({
      notifications: notificationsResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: totalNotifications,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications admin:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des notifications',
      error: error.message
    });
  }
});

// Route pour envoyer une notification (admin)
router.post('/notifications/send', async (req, res) => {
  try {
    const { userId, title, message, type, data, email, push } = req.body;

    if (!userId || !title || !message) {
      return res.status(400).json({ message: 'User ID, title, and message are required' });
    }

    // Import the notification service
    const { notifyUser } = require('../services/notifications');

    const result = await notifyUser({
      userId,
      title,
      message,
      type: type || 'admin',
      data: data || {},
      email: email === true,
      push: push === true,
    });

    res.json(result);
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la notification:', error);
    res.status(500).json({
      message: 'Erreur lors de l\'envoi de la notification',
      error: error.message
    });
  }
});

// Route pour supprimer une notification
router.delete('/notifications/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const deleteQuery = 'DELETE FROM notifications WHERE id = $1 RETURNING *';
    const result = await pool.query(deleteQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Notification non trouvée' });
    }

    res.json({ message: 'Notification supprimée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression de la notification:', error);
    res.status(500).json({
      message: 'Erreur lors de la suppression de la notification',
      error: error.message
    });
  }
});

// Route pour envoyer une notification de test
router.post('/notifications/test', async (req, res) => {
  try {
    const { notifyUser } = require('../services/notifications');

    const result = await notifyUser({
      userId: req.user.id,
      title: 'Notification de test',
      message: 'Ceci est une notification de test envoyée depuis l\'administration.',
      type: 'test',
      data: { test: true },
      email: false,
      push: false,
    });

    res.json(result);
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la notification de test:', error);
    res.status(500).json({
      message: 'Erreur lors de l\'envoi de la notification de test',
      error: error.message
    });
  }
});

// Route pour créer un nouvel utilisateur
router.post('/users', async (req, res) => {
  try {
    const { username, email, first_name, last_name, role, phone, address } = req.body;

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await pool.query('SELECT id FROM users WHERE email = $1 OR username = $2', [email, username]);
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ message: 'Un utilisateur avec cet email ou nom d\'utilisateur existe déjà' });
    }

    const insertQuery = `
      INSERT INTO users (username, email, first_name, last_name, role, phone, address, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      RETURNING id, username, email, first_name, last_name, role, phone, address, created_at
    `;

    const result = await pool.query(insertQuery, [username, email, first_name, last_name, role, phone, address]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Erreur lors de la création de l\'utilisateur:', error);
    res.status(500).json({
      message: 'Erreur lors de la création de l\'utilisateur',
      error: error.message
    });
  }
});

// Route pour modifier un utilisateur
router.put('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, first_name, last_name, role, phone, address } = req.body;

    const updateQuery = `
      UPDATE users
      SET username = $1, email = $2, first_name = $3, last_name = $4, role = $5, phone = $6, address = $7, updated_at = NOW()
      WHERE id = $8
      RETURNING id, username, email, first_name, last_name, role, phone, address, created_at, updated_at
    `;

    const result = await pool.query(updateQuery, [username, email, first_name, last_name, role, phone, address, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erreur lors de la modification de l\'utilisateur:', error);
    res.status(500).json({
      message: 'Erreur lors de la modification de l\'utilisateur',
      error: error.message
    });
  }
});

// Route pour supprimer un utilisateur
router.delete('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que l'utilisateur n'est pas un admin (sécurité)
    const userCheck = await pool.query('SELECT role FROM users WHERE id = $1', [id]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    if (userCheck.rows[0].role === 'admin') {
      return res.status(403).json({ message: 'Impossible de supprimer un administrateur' });
    }

    const deleteQuery = 'DELETE FROM users WHERE id = $1 RETURNING id';
    const result = await pool.query(deleteQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    res.json({ message: 'Utilisateur supprimé avec succès', id: result.rows[0].id });
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'utilisateur:', error);
    res.status(500).json({
      message: 'Erreur lors de la suppression de l\'utilisateur',
      error: error.message
    });
  }
});

module.exports = router;
