const express = require('express');
const app = express();
const PORT = 3003;

// CORS middleware simple
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');
  res.header('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

app.use(express.json());

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Requête reçue sur /');
  res.json({
    message: 'Serveur de test CORS fonctionnel',
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// Route API de test
app.get('/api/test', (req, res) => {
  console.log('✅ Requête reçue sur /api/test');
  res.json({
    message: 'API de test fonctionnelle',
    cors: 'configuré pour localhost:5173'
  });
});

// Route d'authentification
app.post('/api/auth/login', (req, res) => {
  console.log('✅ Requête de connexion reçue:', req.body);
  console.log('📋 Headers reçus:', req.headers);

  const { email, password, firebase_token, firebase_uid } = req.body;

  // Supporter à la fois l'authentification simple et Firebase
  if (email === '<EMAIL>' && password === 'admin123') {
    console.log('✅ Authentification réussie');
    res.json({
      message: 'Connexion réussie',
      token: 'fake-jwt-token-for-testing',
      user: {
        id: 1,
        email: '<EMAIL>',
        role: 'admin',
        nom: 'Admin',
        prenom: 'Poultray',
        first_name: 'Admin',
        last_name: 'Poultray'
      }
    });
  } else {
    console.log('❌ Authentification échouée pour:', { email, password });
    res.status(401).json({
      message: 'Identifiants invalides'
    });
  }
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test CORS démarré sur le port ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📊 API: http://localhost:${PORT}/api/test`);
  console.log(`🔐 Login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`🔧 CORS configuré pour: http://localhost:5173`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
