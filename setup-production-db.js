const { Pool } = require('pg');
require('dotenv').config();

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: 'postgres', // Se connecter à la DB par défaut pour créer la DB cible
  password: process.env.DB_PASSWORD || 'root',
  port: process.env.DB_PORT || 5432,
});

async function setupDatabase() {
  try {
    console.log('🔧 Configuration de la base de données de production...');
    
    // Créer la base de données si elle n'existe pas
    const dbName = process.env.DB_NAME || 'poultraydz';
    
    try {
      await pool.query(`CREATE DATABASE ${dbName}`);
      console.log(`✅ Base de données '${dbName}' créée avec succès`);
    } catch (err) {
      if (err.code === '42P04') {
        console.log(`✅ Base de données '${dbName}' existe déjà`);
      } else {
        throw err;
      }
    }
    
    // Se connecter à la base de données cible
    const targetPool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: dbName,
      password: process.env.DB_PASSWORD || 'root',
      port: process.env.DB_PORT || 5432,
    });
    
    // Créer les tables de base
    console.log('📋 Création des tables de base...');
    
    // Table users
    await targetPool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255),
        first_name VARCHAR(255),
        last_name VARCHAR(255),
        role VARCHAR(50) DEFAULT 'user',
        phone VARCHAR(20),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Table roles
    await targetPool.query(`
      CREATE TABLE IF NOT EXISTS roles (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        permissions JSONB DEFAULT '[]',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Table subscription_plans
    await targetPool.query(`
      CREATE TABLE IF NOT EXISTS subscription_plans (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        duration_days INTEGER NOT NULL,
        features JSONB DEFAULT '[]',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Table notifications
    await targetPool.query(`
      CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(50) DEFAULT 'info',
        data JSONB DEFAULT '{}',
        read BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Table blog_posts
    await targetPool.query(`
      CREATE TABLE IF NOT EXISTS blog_posts (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        excerpt TEXT,
        author_id INTEGER REFERENCES users(id),
        status VARCHAR(20) DEFAULT 'draft',
        featured_image VARCHAR(500),
        tags JSONB DEFAULT '[]',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Insérer un utilisateur admin par défaut
    const adminExists = await targetPool.query(
      "SELECT id FROM users WHERE email = '<EMAIL>'"
    );
    
    if (adminExists.rows.length === 0) {
      await targetPool.query(`
        INSERT INTO users (username, email, first_name, last_name, role, phone, address)
        VALUES ('admin', '<EMAIL>', 'Admin', 'Poultray', 'admin', '+213 123 456 789', 'Alger, Algérie')
      `);
      console.log('✅ Utilisateur admin créé');
    }
    
    // Insérer des rôles par défaut
    const rolesData = [
      { name: 'admin', description: 'Administrateur système', permissions: '["all"]' },
      { name: 'eleveur', description: 'Éleveur de volailles', permissions: '["create_posts", "manage_livestock"]' },
      { name: 'veterinaire', description: 'Vétérinaire', permissions: '["view_health", "create_reports"]' },
      { name: 'marchand', description: 'Marchand', permissions: '["view_marketplace", "create_orders"]' }
    ];
    
    for (const role of rolesData) {
      try {
        await targetPool.query(
          'INSERT INTO roles (name, description, permissions) VALUES ($1, $2, $3) ON CONFLICT (name) DO NOTHING',
          [role.name, role.description, role.permissions]
        );
      } catch (err) {
        console.log(`Rôle ${role.name} existe déjà`);
      }
    }
    
    // Insérer des plans d'abonnement par défaut
    const plansData = [
      { name: 'Basic', description: 'Plan de base', price: 2000, duration: 30, features: '["basic_listing"]' },
      { name: 'Premium', description: 'Plan premium', price: 5000, duration: 30, features: '["premium_listing", "analytics", "priority_support"]' }
    ];
    
    for (const plan of plansData) {
      try {
        await targetPool.query(
          'INSERT INTO subscription_plans (name, description, price, duration_days, features) VALUES ($1, $2, $3, $4, $5) ON CONFLICT DO NOTHING',
          [plan.name, plan.description, plan.price, plan.duration, plan.features]
        );
      } catch (err) {
        console.log(`Plan ${plan.name} existe déjà ou erreur:`, err.message);
      }
    }
    
    console.log('✅ Base de données de production configurée avec succès !');
    console.log('📊 Tables créées : users, roles, subscription_plans, notifications, blog_posts');
    console.log('👤 Utilisateur admin : <EMAIL>');
    
    await targetPool.end();
    await pool.end();
    
  } catch (error) {
    console.error('❌ Erreur lors de la configuration de la base de données:', error);
    process.exit(1);
  }
}

// Exécuter la configuration
setupDatabase();
