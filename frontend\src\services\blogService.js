import axiosInstance from '../utils/axiosConfig';

const blogService = {
  // Récupérer tous les articles avec pagination
  getPosts: async (page = 1, limit = 10, status = null) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      if (status) params.append('status', status);

      const response = await axiosInstance.get(`/blog/posts?${params}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des articles');
    }
  },

  // Récupérer un article par son ID
  getPostById: async (id) => {
    try {
      const response = await axiosInstance.get(`/blog/posts/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'article');
    }
  },

  // Créer un nouvel article
  createPost: async (postData) => {
    try {
      const response = await axiosInstance.post('/blog/posts', postData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'article');
    }
  },

  // Mettre à jour un article
  updatePost: async (id, postData) => {
    try {
      const response = await axiosInstance.put(`/blog/posts/${id}`, postData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour de l\'article');
    }
  },

  // Supprimer un article
  deletePost: async (id) => {
    try {
      const response = await axiosInstance.delete(`/blog/posts/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'article');
    }
  },

  // Récupérer les articles pour l'administration
  getAdminPosts: async (page = 1, limit = 10) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await axiosInstance.get(`/blog/admin/posts?${params}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des articles');
    }
  },
};

export default blogService;
