const cors = require('cors');
const express = require('express');
const { Pool } = require('pg');
require('dotenv').config();

const app = express();

// Configuration CORS
app.use(cors({
  origin: 'http://localhost:5173',
  credentials: true,
  exposedHeaders: ['x-auth-token-refreshed']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configuration de la base de données
let pool;
let dbConnected = false;

async function initDatabase() {
  try {
    pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'poultraydz',
      password: process.env.DB_PASSWORD || 'root',
      port: process.env.DB_PORT || 5432,
    });

    // Test de connexion
    await pool.query('SELECT NOW()');
    console.log('✅ Connexion PostgreSQL établie');
    dbConnected = true;

    // Créer les tables si elles n'existent pas
    await createTables();

  } catch (error) {
    console.log('⚠️ PostgreSQL non disponible, utilisation de données simulées');
    console.log('Erreur:', error.message);
    dbConnected = false;
  }
}

async function createTables() {
  if (!dbConnected) return;

  try {
    // Table users
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255),
        first_name VARCHAR(255),
        last_name VARCHAR(255),
        role VARCHAR(50) DEFAULT 'user',
        phone VARCHAR(20),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Insérer un utilisateur admin par défaut
    const adminExists = await pool.query(
      "SELECT id FROM users WHERE email = '<EMAIL>'"
    );

    if (adminExists.rows.length === 0) {
      await pool.query(`
        INSERT INTO users (username, email, first_name, last_name, role, phone, address)
        VALUES ('admin', '<EMAIL>', 'Admin', 'Poultray', 'admin', '+213 123 456 789', 'Alger, Algérie')
      `);
      console.log('✅ Utilisateur admin créé');
    }

    console.log('✅ Tables créées/vérifiées');
  } catch (error) {
    console.error('Erreur lors de la création des tables:', error);
  }
}

// Middleware de logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Middleware d'authentification simple
const auth = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '') || req.header('x-auth-token');

  if (!token) {
    return res.status(401).json({ message: 'Token manquant' });
  }

  // Authentification simple pour les tests
  if (token === 'fake-jwt-token-for-testing') {
    req.user = { id: 1, email: '<EMAIL>', role: 'admin' };
    next();
  } else {
    res.status(401).json({ message: 'Token invalide' });
  }
};

// Middleware admin
const admin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ message: 'Accès admin requis' });
  }
};

// Routes d'authentification
app.post('/api/auth/login', async (req, res) => {
  const { email, password } = req.body;

  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      message: 'Connexion réussie',
      token: 'fake-jwt-token-for-testing',
      user: {
        id: 1,
        email: '<EMAIL>',
        role: 'admin',
        nom: 'Admin',
        prenom: 'Poultray',
        first_name: 'Admin',
        last_name: 'Poultray'
      }
    });
  } else {
    res.status(401).json({ message: 'Identifiants invalides' });
  }
});

app.get('/api/auth/user', auth, async (req, res) => {
  res.json({
    id: 1,
    email: '<EMAIL>',
    role: 'admin',
    nom: 'Admin',
    prenom: 'Poultray',
    first_name: 'Admin',
    last_name: 'Poultray',
    username: 'admin',
    phone: '+213 123 456 789',
    address: 'Alger, Algérie'
  });
});

// Routes admin
app.get('/api/admin/stats', auth, admin, async (req, res) => {
  try {
    let stats;

    if (dbConnected) {
      const userStats = await pool.query('SELECT role, COUNT(*) as count FROM users GROUP BY role');
      const roleStats = {};
      userStats.rows.forEach(row => {
        roleStats[row.role] = parseInt(row.count);
      });

      stats = {
        users: userStats.rows,
        volailles: { total_volailles: 150, total_eleveurs: 25, prix_moyen: 2500 },
        ventes: { total_ventes: 45, chiffre_affaires_total: 125000 },
        totalEleveurs: roleStats['eleveur'] || 0,
        totalVeterinaires: roleStats['veterinaire'] || 0,
        totalMarchands: roleStats['marchand'] || 0,
        totalAdmins: roleStats['admin'] || 1,
        totalUsers: userStats.rows.reduce((sum, row) => sum + parseInt(row.count), 0)
      };
    } else {
      // Données simulées
      stats = {
        users: [
          { role: 'admin', count: '1' },
          { role: 'eleveur', count: '25' },
          { role: 'veterinaire', count: '8' },
          { role: 'marchand', count: '12' }
        ],
        volailles: { total_volailles: 150, total_eleveurs: 25, prix_moyen: 2500 },
        ventes: { total_ventes: 45, chiffre_affaires_total: 125000 },
        totalEleveurs: 25,
        totalVeterinaires: 8,
        totalMarchands: 12,
        totalAdmins: 1,
        totalUsers: 46
      };
    }

    res.json(stats);
  } catch (error) {
    console.error('Erreur stats:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

app.get('/api/admin/users', auth, admin, async (req, res) => {
  try {
    const { role, page = 1, limit = 10 } = req.query;
    console.log(`📊 Requête /admin/users - role: ${role}, page: ${page}, limit: ${limit}`);

    let users = [];
    let total = 0;

    if (dbConnected) {
      try {
        let query = 'SELECT id, username, email, first_name, last_name, role, phone, address, created_at FROM users';
        let countQuery = 'SELECT COUNT(*) as total FROM users';
        const params = [];
        const countParams = [];

        if (role) {
          query += ' WHERE role = $1';
          countQuery += ' WHERE role = $1';
          params.push(role);
          countParams.push(role);
        }

        query += ' ORDER BY created_at DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
        params.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));

        console.log(`🔍 Exécution requête: ${query} avec params:`, params);

        const [result, countResult] = await Promise.all([
          pool.query(query, params),
          pool.query(countQuery, countParams)
        ]);

        users = result.rows;
        total = parseInt(countResult.rows[0].total);

        console.log(`✅ Résultats: ${users.length} utilisateurs trouvés sur ${total} total`);
      } catch (dbError) {
        console.error('❌ Erreur base de données:', dbError);
        // Fallback vers données simulées
        users = getSimulatedUsers(role);
        total = users.length;
      }
    } else {
      // Données simulées
      users = getSimulatedUsers(role);
      total = users.length;
    }

    res.json({
      users,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / parseInt(limit))
    });
  } catch (error) {
    console.error('❌ Erreur endpoint /admin/users:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Fonction pour générer des utilisateurs simulés
function getSimulatedUsers(role) {
  const allUsers = [
    { id: 1, username: 'admin', email: '<EMAIL>', first_name: 'Admin', last_name: 'Poultray', role: 'admin', phone: '+213 123 456 789', address: 'Alger, Algérie', created_at: new Date() },
    { id: 2, username: 'eleveur1', email: '<EMAIL>', first_name: 'Ahmed', last_name: 'Benali', role: 'eleveur', phone: '+213 555 123 456', address: 'Blida, Algérie', created_at: new Date() },
    { id: 3, username: 'eleveur2', email: '<EMAIL>', first_name: 'Fatima', last_name: 'Khelifi', role: 'eleveur', phone: '+213 555 789 012', address: 'Tizi Ouzou, Algérie', created_at: new Date() },
    { id: 4, username: 'vet1', email: '<EMAIL>', first_name: 'Dr. Karim', last_name: 'Mansouri', role: 'veterinaire', phone: '+213 555 345 678', address: 'Oran, Algérie', created_at: new Date() },
    { id: 5, username: 'marchand1', email: '<EMAIL>', first_name: 'Omar', last_name: 'Boudiaf', role: 'marchand', phone: '+213 555 901 234', address: 'Constantine, Algérie', created_at: new Date() }
  ];

  if (role) {
    return allUsers.filter(user => user.role === role);
  }

  return allUsers;
}

// Route pour les données de plateforme
app.get('/api/admin/data', auth, admin, async (req, res) => {
  try {
    const { type = 'sales', timeRange = 'month' } = req.query;

    // Générer des données simulées
    const data = generateMockData(timeRange, type);
    const total = data.reduce((sum, item) => sum + item.value, 0);
    const growth = Math.random() * 20 - 5; // Entre -5% et +15%

    res.json({
      data,
      total,
      growth: parseFloat(growth.toFixed(2)),
      type,
      timeRange
    });
  } catch (error) {
    console.error('Erreur data:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Route pour les rôles
app.get('/api/admin/roles', auth, admin, async (req, res) => {
  try {
    const roles = [
      { id: 1, name: 'admin', description: 'Administrateur système', permissions: ['all'], is_active: true },
      { id: 2, name: 'eleveur', description: 'Éleveur de volailles', permissions: ['create_posts', 'manage_livestock'], is_active: true },
      { id: 3, name: 'veterinaire', description: 'Vétérinaire', permissions: ['view_health', 'create_reports'], is_active: true },
      { id: 4, name: 'marchand', description: 'Marchand', permissions: ['view_marketplace', 'create_orders'], is_active: true }
    ];

    res.json({ roles });
  } catch (error) {
    console.error('Erreur roles:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Route pour les plans d'abonnement
app.get('/api/admin/subscription-plans', auth, admin, async (req, res) => {
  try {
    const plans = [
      { id: 1, name: 'Basic', description: 'Plan de base', price: 2000, duration_days: 30, features: ['basic_listing'], is_active: true },
      { id: 2, name: 'Premium', description: 'Plan premium', price: 5000, duration_days: 30, features: ['premium_listing', 'analytics', 'priority_support'], is_active: true },
      { id: 3, name: 'Enterprise', description: 'Plan entreprise', price: 10000, duration_days: 30, features: ['unlimited_listings', 'advanced_analytics', 'dedicated_support'], is_active: true }
    ];

    res.json({ plans });
  } catch (error) {
    console.error('Erreur plans:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Route pour les notifications
app.get('/api/admin/notifications', auth, admin, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const notifications = [
      { id: 1, title: 'Nouveau utilisateur', message: 'Un nouvel éleveur s\'est inscrit', read: false, created_at: new Date(), user_id: 1 },
      { id: 2, title: 'Vente réalisée', message: 'Une vente de 50 poulets a été effectuée', read: false, created_at: new Date(), user_id: 2 },
      { id: 3, title: 'Rapport vétérinaire', message: 'Nouveau rapport de santé disponible', read: true, created_at: new Date(), user_id: 3 }
    ];

    res.json({
      notifications,
      pagination: {
        currentPage: parseInt(page),
        totalPages: 1,
        totalItems: notifications.length,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Erreur notifications:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Route pour les articles de blog admin
app.get('/api/blog/admin/posts', auth, admin, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const posts = [
      { id: 1, title: 'Guide de l\'élevage de poulets', content: 'Contenu du guide...', status: 'published', author: 'admin', created_at: new Date() },
      { id: 2, title: 'Conseils vétérinaires', content: 'Conseils pour la santé...', status: 'draft', author: 'admin', created_at: new Date() },
      { id: 3, title: 'Tendances du marché', content: 'Analyse du marché...', status: 'published', author: 'admin', created_at: new Date() }
    ];

    res.json({
      posts,
      total: posts.length,
      page: parseInt(page),
      totalPages: Math.ceil(posts.length / limit)
    });
  } catch (error) {
    console.error('Erreur blog posts:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Fonction utilitaire pour générer des données simulées
function generateMockData(timeRange, type) {
  const data = [];
  const now = new Date();
  let days = 30;

  switch (timeRange) {
    case 'day': days = 1; break;
    case 'week': days = 7; break;
    case 'month': days = 30; break;
    case 'year': days = 365; break;
  }

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);

    let value;
    switch (type) {
      case 'sales':
        value = Math.floor(Math.random() * 50) + 10;
        break;
      case 'users':
        value = Math.floor(Math.random() * 20) + 5;
        break;
      case 'revenue':
        value = Math.floor(Math.random() * 10000) + 2000;
        break;
      default:
        value = Math.floor(Math.random() * 100) + 10;
    }

    data.push({
      date: date.toISOString().split('T')[0],
      value
    });
  }

  return data;
}

// Route de base
app.get('/', (req, res) => {
  res.json({
    message: 'Backend Poultray DZ - Production',
    status: 'running',
    database: dbConnected ? 'PostgreSQL' : 'Simulated data',
    timestamp: new Date().toISOString()
  });
});

// Initialiser et démarrer le serveur
async function startServer() {
  await initDatabase();

  const PORT = 3003; // Force le port 3003
  app.listen(PORT, () => {
    console.log(`🚀 Serveur backend démarré sur le port ${PORT}`);
    console.log(`📊 Base de données: ${dbConnected ? 'PostgreSQL connectée' : 'Données simulées'}`);
    console.log(`🌐 CORS configuré pour: http://localhost:5173`);
    console.log(`👤 Admin: <EMAIL> / admin123`);
  });
}

startServer();
