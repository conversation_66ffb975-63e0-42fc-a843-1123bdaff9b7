{"version": 3, "sources": ["../../hoist-non-react-statics/node_modules/react-is/cjs/react-is.development.js", "../../hoist-non-react-statics/node_modules/react-is/index.js", "../../hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAI,UAAU;AAMd,QAAI,gBAAgB;AAAA,MAClB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,0BAA0B;AAAA,MAC1B,0BAA0B;AAAA,MAC1B,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,QAAI,gBAAgB;AAAA,MAClB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AACA,QAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AACA,QAAI,eAAe;AAAA,MACjB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,QAAI,eAAe,CAAC;AACpB,iBAAa,QAAQ,UAAU,IAAI;AACnC,iBAAa,QAAQ,IAAI,IAAI;AAE7B,aAAS,WAAW,WAAW;AAE7B,UAAI,QAAQ,OAAO,SAAS,GAAG;AAC7B,eAAO;AAAA,MACT;AAGA,aAAO,aAAa,UAAU,UAAU,CAAC,KAAK;AAAA,IAChD;AAEA,QAAI,iBAAiB,OAAO;AAC5B,QAAI,sBAAsB,OAAO;AACjC,QAAI,wBAAwB,OAAO;AACnC,QAAI,2BAA2B,OAAO;AACtC,QAAI,iBAAiB,OAAO;AAC5B,QAAI,kBAAkB,OAAO;AAC7B,aAAS,qBAAqB,iBAAiB,iBAAiB,WAAW;AACzE,UAAI,OAAO,oBAAoB,UAAU;AAEvC,YAAI,iBAAiB;AACnB,cAAI,qBAAqB,eAAe,eAAe;AAEvD,cAAI,sBAAsB,uBAAuB,iBAAiB;AAChE,iCAAqB,iBAAiB,oBAAoB,SAAS;AAAA,UACrE;AAAA,QACF;AAEA,YAAI,OAAO,oBAAoB,eAAe;AAE9C,YAAI,uBAAuB;AACzB,iBAAO,KAAK,OAAO,sBAAsB,eAAe,CAAC;AAAA,QAC3D;AAEA,YAAI,gBAAgB,WAAW,eAAe;AAC9C,YAAI,gBAAgB,WAAW,eAAe;AAE9C,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAAC,cAAc,GAAG,KAAK,EAAE,aAAa,UAAU,GAAG,MAAM,EAAE,iBAAiB,cAAc,GAAG,MAAM,EAAE,iBAAiB,cAAc,GAAG,IAAI;AAC7I,gBAAI,aAAa,yBAAyB,iBAAiB,GAAG;AAE9D,gBAAI;AAEF,6BAAe,iBAAiB,KAAK,UAAU;AAAA,YACjD,SAAS,GAAG;AAAA,YAAC;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}