import React, { createContext, useState, useEffect, useContext } from 'react';
import api from '../services/api';
import { firebaseAuth } from '../services/firebaseAuth';
import { simpleAuth } from '../services/simpleAuth';
import { auth } from '../services/firebaseConfig';

// Mode de test - utiliser simpleAuth au lieu de firebaseAuth
const USE_SIMPLE_AUTH = true;

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // L'instance api gère automatiquement les tokens via les intercepteurs
  useEffect(() => {
    if (!token) {
      setLoading(false);
      return;
    }
  }, [token]);

  // Load user data if token exists
  useEffect(() => {
    const loadUser = async () => {
      console.log('🔍 AuthContext: Chargement utilisateur, token:', !!token);
      if (!token) {
        console.log('❌ AuthContext: Pas de token, arrêt du chargement');
        setLoading(false);
        return;
      }

      try {
        console.log('📡 AuthContext: Appel API /auth/user');
        const res = await api.get('/auth/user');
        console.log('✅ AuthContext: Utilisateur chargé:', res.data);
        setUser(res.data);
        setError(null);
      } catch (err) {
        console.error('❌ AuthContext: Erreur chargement utilisateur:', err);
        setError('Session expirée. Veuillez vous reconnecter.');
        localStorage.removeItem('token');
        setToken(null);
        setUser(null);
      } finally {
        console.log('🏁 AuthContext: Fin du chargement');
        setLoading(false);
      }
    };

    loadUser();
  }, [token]);

  // Login user
  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      // Utiliser simpleAuth ou firebaseAuth selon le mode
      const authService = USE_SIMPLE_AUTH ? simpleAuth : firebaseAuth;
      console.log(`🔧 AuthContext: Utilisation de ${USE_SIMPLE_AUTH ? 'simpleAuth' : 'firebaseAuth'}`);

      const authData = await authService.login(email, password);

      // Le token est déjà stocké dans localStorage par le service d'auth
      setToken(authData.token);
      setUser(authData.user);

      return authData.user;
    } catch (err) {
      console.error('Erreur de connexion:', err);
      setError(err.message || 'Erreur de connexion');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Register user
  const register = async (userData) => {
    try {
      setLoading(true);
      // Utiliser Firebase Auth pour l'inscription
      const authData = await firebaseAuth.register(userData);
      return authData;
    } catch (err) {
      setError(err.message || 'Erreur d\'inscription');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = async () => {
    try {
      // Utiliser simpleAuth ou firebaseAuth selon le mode
      const authService = USE_SIMPLE_AUTH ? simpleAuth : firebaseAuth;
      await authService.logout();
      setToken(null);
      setUser(null);
    } catch (err) {
      console.error('Erreur lors de la déconnexion:', err);
      setError(err.message || 'Erreur lors de la déconnexion');
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      const res = await api.put('/api/auth/profile', profileData);
      setUser(res.data);
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur de mise à jour du profil');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Change password
  const changePassword = async (oldPassword, newPassword) => {
    try {
      setLoading(true);
      const res = await api.put('/api/auth/change-password', { oldPassword, newPassword });
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur de changement de mot de passe');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    return user && user.role === role;
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  const value = {
    user,
    token,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    hasRole,
    isAuthenticated,
    setError
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
